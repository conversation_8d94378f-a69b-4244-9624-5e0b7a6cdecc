import axios from 'axios'
import { UploadResponse, SceneData } from '../types'

const API_BASE_URL = '/api'

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
})

export const uploadPDF = async (file: File): Promise<UploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response = await api.post('/upload/', formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    })

    return response.data
}

export const savePDF = async (fileId: number, sceneData: SceneData): Promise<Blob> => {
    const response = await api.post(`/save/${fileId}`, sceneData, {
        responseType: 'blob',
    })

    return response.data
}

export interface SplitRange {
    start: number
    end: number
    name?: string
}

export interface SplitRequest {
    mode: 'custom' | 'fixed'
    ranges?: SplitRange[]
    pages_per_file?: number
}

export interface SplitPreview {
    total_pages: number
    total_files: number
    pages_per_file?: number
    files: Array<{
        name: string
        start_page: number
        end_page: number
        page_count: number
        size_estimate: string
    }>
}

export const splitPDF = async (fileId: number, splitRequest: SplitRequest): Promise<Blob> => {
    const response = await api.post(`/split/${fileId}`, splitRequest, {
        responseType: 'blob',
    })

    return response.data
}

export const getSplitPreview = async (fileId: number, splitRequest: SplitRequest): Promise<SplitPreview> => {
    const response = await api.post(`/split-preview/${fileId}`, splitRequest)

    return response.data
}

export interface ThumbnailResponse {
    thumbnails: string[]
}

export const getAllThumbnails = async (fileId: number): Promise<ThumbnailResponse> => {
    const response = await api.get(`/thumbnails/${fileId}`)
    return response.data
}

export const getPageThumbnail = async (fileId: number, pageNumber: number): Promise<{ thumbnail: string }> => {
    const response = await api.get(`/thumbnail/${fileId}/${pageNumber}`)
    return response.data
}

export const healthCheck = async (): Promise<{ status: string }> => {
    const response = await api.get('/health')
    return response.data
}
