import React, { useState, useRef, useEffect } from 'react'
import { Stage, Layer, Text, Image as KonvaImage, Rect, Transformer } from 'react-konva'
import { PageData, TextBlock, ImageBlock, EditableElement } from '../types'
import TextEditor from './TextEditor'
import './PDFPageEditor.css'

interface PDFPageEditorProps {
    pageData: PageData
    onPageUpdate: (updatedPage: PageData) => void
    zoom?: number
    selectedElementId?: string | null
    onElementSelect?: (id: string | null) => void
}

const PDFPageEditor: React.FC<PDFPageEditorProps> = ({
    pageData,
    onPageUpdate,
    zoom = 1,
    selectedElementId,
    onElementSelect
}) => {
    const [selectedId, setSelectedId] = useState<string | null>(selectedElementId || null)
    const [editingText, setEditingText] = useState<string | null>(null)
    const [scale, setScale] = useState(zoom)
    const stageRef = useRef<any>(null)
    const transformerRef = useRef<any>(null)
    const [images, setImages] = useState<{ [key: string]: HTMLImageElement }>({})

    // Sync with external props
    useEffect(() => {
        setScale(zoom)
    }, [zoom])

    useEffect(() => {
        setSelectedId(selectedElementId || null)
    }, [selectedElementId])

    // Load images
    useEffect(() => {
        const loadImages = async () => {
            const imagePromises = pageData.images.map(async (img) => {
                return new Promise<{ id: string; image: HTMLImageElement }>((resolve) => {
                    const image = new window.Image()
                    image.onload = () => resolve({ id: img.id, image })
                    image.src = `data:image/png;base64,${img.data}`
                })
            })

            const loadedImages = await Promise.all(imagePromises)
            const imageMap = loadedImages.reduce((acc, { id, image }) => {
                acc[id] = image
                return acc
            }, {} as { [key: string]: HTMLImageElement })

            setImages(imageMap)
        }

        loadImages()
    }, [pageData.images])

    // Update transformer when selection changes
    useEffect(() => {
        if (selectedId && transformerRef.current) {
            const stage = stageRef.current
            const selectedNode = stage.findOne(`#${selectedId}`)
            if (selectedNode) {
                transformerRef.current.nodes([selectedNode])
                transformerRef.current.getLayer().batchDraw()
            }
        } else if (transformerRef.current) {
            transformerRef.current.nodes([])
            transformerRef.current.getLayer().batchDraw()
        }
    }, [selectedId])

    const handleStageClick = (e: any) => {
        // Deselect when clicking on empty area
        if (e.target === e.target.getStage()) {
            setSelectedId(null)
            setEditingText(null)
            onElementSelect?.(null)
        }
    }

    const handleElementSelect = (id: string) => {
        setSelectedId(id)
        setEditingText(null)
        onElementSelect?.(id)
    }

    const handleTextDoubleClick = (textBlock: TextBlock) => {
        setEditingText(textBlock.id.toString())
        setSelectedId(null)
    }

    const handleTextUpdate = (id: string, newText: string) => {
        const updatedTextBlocks = pageData.text_blocks.map(block =>
            block.id.toString() === id
                ? { ...block, text: newText, action: 'update' as const }
                : block
        )

        onPageUpdate({
            ...pageData,
            text_blocks: updatedTextBlocks
        })

        setEditingText(null)
    }

    const handleElementTransform = (id: string, newAttrs: any) => {
        // Find the element and update its position/size
        const textBlockIndex = pageData.text_blocks.findIndex(block => block.id.toString() === id)
        if (textBlockIndex !== -1) {
            const updatedTextBlocks = [...pageData.text_blocks]
            const block = updatedTextBlocks[textBlockIndex]
            updatedTextBlocks[textBlockIndex] = {
                ...block,
                bbox: [newAttrs.x, newAttrs.y, newAttrs.x + newAttrs.width, newAttrs.y + newAttrs.height],
                action: 'update' as const
            }
            onPageUpdate({
                ...pageData,
                text_blocks: updatedTextBlocks
            })
            return
        }

        const imageIndex = pageData.images.findIndex(img => img.id === id)
        if (imageIndex !== -1) {
            const updatedImages = [...pageData.images]
            const img = updatedImages[imageIndex]
            updatedImages[imageIndex] = {
                ...img,
                bbox: [newAttrs.x, newAttrs.y, newAttrs.x + newAttrs.width, newAttrs.y + newAttrs.height],
                action: 'update' as const
            }
            onPageUpdate({
                ...pageData,
                images: updatedImages
            })
        }
    }

    const handleDelete = () => {
        if (!selectedId) return

        // Mark element for deletion
        const textBlockIndex = pageData.text_blocks.findIndex(block => block.id.toString() === selectedId)
        if (textBlockIndex !== -1) {
            const updatedTextBlocks = [...pageData.text_blocks]
            updatedTextBlocks[textBlockIndex] = {
                ...updatedTextBlocks[textBlockIndex],
                action: 'delete' as const
            }
            onPageUpdate({
                ...pageData,
                text_blocks: updatedTextBlocks
            })
            setSelectedId(null)
            return
        }

        const imageIndex = pageData.images.findIndex(img => img.id === selectedId)
        if (imageIndex !== -1) {
            const updatedImages = [...pageData.images]
            updatedImages[imageIndex] = {
                ...updatedImages[imageIndex],
                action: 'delete' as const
            }
            onPageUpdate({
                ...pageData,
                images: updatedImages
            })
            setSelectedId(null)
        }
    }

    // Handle keyboard events
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Delete' || e.key === 'Backspace') {
                handleDelete()
            }
        }

        window.addEventListener('keydown', handleKeyDown)
        return () => window.removeEventListener('keydown', handleKeyDown)
    }, [selectedId])

    const stageWidth = Math.max(800, pageData.width * scale)
    const stageHeight = Math.max(600, pageData.height * scale)

    return (
        <div className="pdf-page-editor">
            <div className="stage-container">
                <Stage
                    ref={stageRef}
                    width={stageWidth}
                    height={stageHeight}
                    onClick={handleStageClick}
                    scaleX={scale}
                    scaleY={scale}
                >
                    <Layer>
                        {/* Page background */}
                        <Rect
                            width={pageData.width}
                            height={pageData.height}
                            fill="white"
                            stroke="#ddd"
                            strokeWidth={1}
                        />

                        {/* Text blocks */}
                        {pageData.text_blocks
                            .filter(block => block.action !== 'delete')
                            .map((textBlock) => (
                                <Text
                                    key={textBlock.id}
                                    id={textBlock.id.toString()}
                                    text={textBlock.text}
                                    x={textBlock.bbox[0]}
                                    y={textBlock.bbox[1]}
                                    width={textBlock.bbox[2] - textBlock.bbox[0]}
                                    height={textBlock.bbox[3] - textBlock.bbox[1]}
                                    fontSize={textBlock.size}
                                    fill={textBlock.color ? `#${textBlock.color.toString(16).padStart(6, '0')}` : '#000000'}
                                    draggable
                                    onClick={() => handleElementSelect(textBlock.id.toString())}
                                    onDblClick={() => handleTextDoubleClick(textBlock)}
                                    onDragEnd={(e) => {
                                        handleElementTransform(textBlock.id.toString(), {
                                            x: e.target.x(),
                                            y: e.target.y(),
                                            width: e.target.width(),
                                            height: e.target.height()
                                        })
                                    }}
                                    onTransformEnd={(e) => {
                                        handleElementTransform(textBlock.id.toString(), {
                                            x: e.target.x(),
                                            y: e.target.y(),
                                            width: e.target.width() * e.target.scaleX(),
                                            height: e.target.height() * e.target.scaleY()
                                        })
                                        e.target.scaleX(1)
                                        e.target.scaleY(1)
                                    }}
                                />
                            ))}

                        {/* Images */}
                        {pageData.images
                            .filter(img => img.action !== 'delete')
                            .map((imageBlock) => {
                                const image = images[imageBlock.id]
                                if (!image) return null

                                return (
                                    <KonvaImage
                                        key={imageBlock.id}
                                        id={imageBlock.id}
                                        image={image}
                                        x={imageBlock.bbox[0]}
                                        y={imageBlock.bbox[1]}
                                        width={imageBlock.bbox[2] - imageBlock.bbox[0]}
                                        height={imageBlock.bbox[3] - imageBlock.bbox[1]}
                                        draggable
                                        onClick={() => handleElementSelect(imageBlock.id)}
                                        onDragEnd={(e) => {
                                            handleElementTransform(imageBlock.id, {
                                                x: e.target.x(),
                                                y: e.target.y(),
                                                width: e.target.width(),
                                                height: e.target.height()
                                            })
                                        }}
                                        onTransformEnd={(e) => {
                                            handleElementTransform(imageBlock.id, {
                                                x: e.target.x(),
                                                y: e.target.y(),
                                                width: e.target.width() * e.target.scaleX(),
                                                height: e.target.height() * e.target.scaleY()
                                            })
                                            e.target.scaleX(1)
                                            e.target.scaleY(1)
                                        }}
                                    />
                                )
                            })}

                        {/* Transformer for selected elements */}
                        <Transformer
                            ref={transformerRef}
                            boundBoxFunc={(oldBox, newBox) => {
                                // Limit resize
                                if (newBox.width < 5 || newBox.height < 5) {
                                    return oldBox
                                }
                                return newBox
                            }}
                        />
                    </Layer>
                </Stage>
            </div>

            {/* Text editor modal */}
            {editingText && (
                <TextEditor
                    textId={editingText}
                    initialText={pageData.text_blocks.find(b => b.id.toString() === editingText)?.text || ''}
                    onSave={handleTextUpdate}
                    onCancel={() => setEditingText(null)}
                />
            )}
        </div>
    )
}

export default PDFPageEditor
