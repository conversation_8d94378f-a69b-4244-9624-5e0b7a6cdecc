/* Thumbnail Sidebar */
.thumbnail-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;
  height: 100%;
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.page-counter {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Loading State */
.loading-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-muted);
  font-size: 0.875rem;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--info);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error State */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--error);
  font-size: 0.875rem;
  padding: var(--spacing-sm);
  background: var(--error-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--error-border);
}

.error-icon {
  font-size: 1rem;
}

/* Thumbnails Grid */
.thumbnails-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 600px;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
  scrollbar-width: thin;
  scrollbar-color: var(--border-light) transparent;
}

.thumbnails-grid::-webkit-scrollbar {
  width: 6px;
}

.thumbnails-grid::-webkit-scrollbar-track {
  background: transparent;
}

.thumbnails-grid::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 3px;
}

.thumbnails-grid::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1400px) {
  .thumbnail-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar-title {
    font-size: 0.9rem;
  }
  
  .page-counter {
    font-size: 0.8rem;
  }
  
  .thumbnails-grid {
    max-height: 400px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .sidebar-header {
    border-bottom-width: 2px;
  }
  
  .error-message {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .thumbnail-sidebar {
    display: none !important;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .thumbnails-grid::-webkit-scrollbar-thumb {
    background: var(--border-dark);
  }
  
  .thumbnails-grid::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
  }
}
