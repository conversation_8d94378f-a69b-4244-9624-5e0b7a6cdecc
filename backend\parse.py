import fitz  # PyMuPDF
import base64
from typing import List, Dict, Any
import io


def parse_pdf_to_json(pdf_content: bytes) -> List[Dict[str, Any]]:
    """
    Parse PDF content into JSON scene-graph format with enhanced element detection

    Returns:
        List of pages, each containing text_blocks, images, and shapes
    """
    doc = fitz.open(stream=pdf_content, filetype="pdf")
    pages = []

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)

        # Get page dimensions
        page_rect = page.rect
        page_data = {
            "page": page_num,
            "width": page_rect.width,
            "height": page_rect.height,
            "text_blocks": [],
            "images": [],
            "shapes": [],
        }

        # Extract text blocks with better grouping
        text_blocks = page.get_text("dict")
        block_id = 0

        for block in text_blocks["blocks"]:
            if "lines" in block:  # Text block
                # Group text by lines to preserve layout
                for line in block["lines"]:
                    line_text = ""
                    line_bbox = None
                    line_font = None
                    line_size = None
                    line_flags = 0
                    line_color = 0

                    for span in line["spans"]:
                        if span["text"].strip():
                            line_text += span["text"]
                            if line_bbox is None:
                                line_bbox = list(span["bbox"])
                                line_font = span["font"]
                                line_size = span["size"]
                                line_flags = span["flags"]
                                line_color = span["color"]
                            else:
                                # Extend bbox to include this span
                                line_bbox[0] = min(line_bbox[0], span["bbox"][0])
                                line_bbox[1] = min(line_bbox[1], span["bbox"][1])
                                line_bbox[2] = max(line_bbox[2], span["bbox"][2])
                                line_bbox[3] = max(line_bbox[3], span["bbox"][3])

                    if line_text.strip():
                        text_block = {
                            "id": block_id,
                            "text": line_text,
                            "bbox": line_bbox,
                            "font": line_font,
                            "size": line_size,
                            "flags": line_flags,
                            "color": line_color,
                            "type": "text",
                        }
                        page_data["text_blocks"].append(text_block)
                        block_id += 1

        # Add page background as a base image to preserve all visual content
        # This ensures charts, complex graphics, and other elements are preserved
        try:
            # Render page at high resolution to capture all visual elements
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat, alpha=False)
            img_data = pix.tobytes("png")

            # Add as background image
            background_image = {
                "id": "background",
                "xref": -1,  # Special marker for background
                "bbox": [0, 0, page_rect.width, page_rect.height],
                "width": pix.width,
                "height": pix.height,
                "data": base64.b64encode(img_data).decode(),
                "type": "background_image",
                "z_index": -1,  # Ensure it's behind other elements
            }
            page_data["images"].append(background_image)
            pix = None

        except Exception as e:
            print(f"Error creating background image for page {page_num}: {e}")

        # Extract embedded images
        image_list = page.get_images()
        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)

                # Convert to PNG if not already
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("png")
                else:  # CMYK: convert to RGB first
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    img_data = pix1.tobytes("png")
                    pix1 = None

                # Get image rectangle on page
                img_rects = page.get_image_rects(xref)
                if img_rects:
                    bbox = img_rects[0]  # Use first occurrence

                    image_block = {
                        "id": f"img_{img_index}",
                        "xref": xref,
                        "bbox": [bbox.x0, bbox.y0, bbox.x1, bbox.y1],
                        "width": pix.width,
                        "height": pix.height,
                        "data": base64.b64encode(img_data).decode(),
                        "type": "image",
                    }
                    page_data["images"].append(image_block)

                pix = None

            except Exception as e:
                print(f"Error extracting image {img_index}: {e}")
                continue

        # Extract basic shapes (rectangles, lines)
        drawings = page.get_drawings()
        shape_id = 0

        for drawing in drawings:
            try:
                shape_data = {
                    "id": f"shape_{shape_id}",
                    "type": "shape",
                    "items": drawing["items"],
                    "rect": drawing["rect"],
                    "fill": drawing.get("fill"),
                    "stroke": drawing.get("stroke"),
                    "width": drawing.get("width", 1),
                }
                page_data["shapes"].append(shape_data)
                shape_id += 1
            except Exception as e:
                print(f"Error extracting shape {shape_id}: {e}")
                continue

        pages.append(page_data)

    doc.close()
    return pages


def get_pdf_page_count(pdf_content: bytes) -> int:
    """Get the number of pages in a PDF"""
    doc = fitz.open(stream=pdf_content, filetype="pdf")
    count = len(doc)
    doc.close()
    return count
