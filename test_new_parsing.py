#!/usr/bin/env python3

import requests
import json

def test_new_parsing():
    """Test the new parsing approach with background images"""
    
    print("Testing new PDF parsing with background images...")
    
    # Test upload
    print('Testing PDF upload...')
    with open('multi_page_test.pdf', 'rb') as f:
        files = {'file': f}
        response = requests.post('http://localhost:8000/upload/', files=files)

    if response.status_code == 200:
        print('✓ Upload successful')
        data = response.json()
        file_id = data['file_id']
        pages = data['pages']
        
        print(f'File ID: {file_id}')
        print(f'Pages: {len(pages)}')
        
        # Check if background images are present
        for i, page in enumerate(pages):
            print(f'\nPage {i+1}:')
            print(f'  Text blocks: {len(page["text_blocks"])}')
            print(f'  Images: {len(page["images"])}')
            print(f'  Shapes: {len(page["shapes"])}')
            
            # Check for background image
            background_images = [img for img in page["images"] if img.get("type") == "background_image"]
            if background_images:
                bg_img = background_images[0]
                print(f'  ✓ Background image found: {bg_img["width"]}x{bg_img["height"]} pixels')
                print(f'    Data size: {len(bg_img["data"])} characters (base64)')
            else:
                print('  ✗ No background image found')
        
        # Test thumbnail generation
        print('\nTesting thumbnail generation...')
        thumb_response = requests.get(f'http://localhost:8000/thumbnails/{file_id}')
        
        if thumb_response.status_code == 200:
            thumb_data = thumb_response.json()
            thumbnails = thumb_data['thumbnails']
            print(f'✓ Thumbnails generated: {len(thumbnails)} thumbnails')
            for i, thumb in enumerate(thumbnails):
                print(f'  Page {i+1}: {len(thumb)} characters (base64)')
        else:
            print(f'✗ Thumbnail generation failed: {thumb_response.status_code}')
            print(f'Error: {thumb_response.text}')
            
    else:
        print(f'✗ Upload failed: {response.status_code}')
        print(f'Error: {response.text}')

if __name__ == "__main__":
    test_new_parsing()
