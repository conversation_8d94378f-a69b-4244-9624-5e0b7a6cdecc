import fitz  # PyMuPDF
import base64
from typing import List, Dict, Any
import io
from PIL import Image


def generate_page_thumbnail(pdf_content: bytes, page_number: int, max_width: int = 150, max_height: int = 200) -> str:
    """
    Generate a thumbnail for a specific PDF page
    
    Args:
        pdf_content: PDF file content as bytes
        page_number: Page number (0-indexed)
        max_width: Maximum thumbnail width in pixels
        max_height: Maximum thumbnail height in pixels
    
    Returns:
        Base64 encoded thumbnail image (PNG format)
    """
    try:
        # Open PDF document
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        
        # Check if page exists
        if page_number >= len(doc) or page_number < 0:
            doc.close()
            raise ValueError(f"Page {page_number} does not exist in PDF")
        
        # Load the specific page
        page = doc.load_page(page_number)
        
        # Calculate scale to fit within max dimensions while maintaining aspect ratio
        page_rect = page.rect
        scale_x = max_width / page_rect.width
        scale_y = max_height / page_rect.height
        scale = min(scale_x, scale_y)
        
        # Create transformation matrix
        mat = fitz.Matrix(scale, scale)
        
        # Render page to pixmap
        pix = page.get_pixmap(matrix=mat, alpha=False)
        
        # Convert to PNG bytes
        img_data = pix.tobytes("png")
        
        # Clean up
        pix = None
        doc.close()
        
        # Encode to base64
        img_base64 = base64.b64encode(img_data).decode('utf-8')
        
        return f"data:image/png;base64,{img_base64}"
        
    except Exception as e:
        print(f"Error generating thumbnail for page {page_number}: {e}")
        raise


def generate_all_thumbnails(pdf_content: bytes, max_width: int = 150, max_height: int = 200) -> List[str]:
    """
    Generate thumbnails for all pages in a PDF
    
    Args:
        pdf_content: PDF file content as bytes
        max_width: Maximum thumbnail width in pixels
        max_height: Maximum thumbnail height in pixels
    
    Returns:
        List of base64 encoded thumbnail images
    """
    try:
        # Open PDF document to get page count
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        page_count = len(doc)
        doc.close()
        
        # Generate thumbnails for all pages
        thumbnails = []
        for page_num in range(page_count):
            thumbnail = generate_page_thumbnail(pdf_content, page_num, max_width, max_height)
            thumbnails.append(thumbnail)
        
        return thumbnails
        
    except Exception as e:
        print(f"Error generating thumbnails: {e}")
        raise


def get_thumbnail_dimensions(pdf_content: bytes, page_number: int = 0) -> Dict[str, float]:
    """
    Get the original dimensions of a PDF page for thumbnail calculation
    
    Args:
        pdf_content: PDF file content as bytes
        page_number: Page number to get dimensions from (default: 0)
    
    Returns:
        Dictionary with width and height
    """
    try:
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        
        if page_number >= len(doc) or page_number < 0:
            doc.close()
            raise ValueError(f"Page {page_number} does not exist in PDF")
        
        page = doc.load_page(page_number)
        page_rect = page.rect
        
        dimensions = {
            "width": page_rect.width,
            "height": page_rect.height,
            "aspect_ratio": page_rect.width / page_rect.height
        }
        
        doc.close()
        return dimensions
        
    except Exception as e:
        print(f"Error getting page dimensions: {e}")
        raise
