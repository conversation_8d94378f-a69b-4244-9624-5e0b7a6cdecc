import React, { useState, useEffect } from 'react'
import { Stage, Layer, Image as KonvaImage } from 'react-konva'
import { getPageBackground } from '../services/api'

interface BackgroundPDFRendererProps {
    fileId: number
    pageIndex: number
    width: number
    height: number
    children?: React.ReactNode
}

const BackgroundPDFRenderer: React.FC<BackgroundPDFRendererProps> = ({
    fileId,
    pageIndex,
    width,
    height,
    children
}) => {
    const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [scale, setScale] = useState(1)

    useEffect(() => {
        loadBackgroundImage()
    }, [fileId, pageIndex])

    const loadBackgroundImage = async () => {
        try {
            setLoading(true)
            setError(null)

            console.log(`Loading background for file ${fileId}, page ${pageIndex}`)

            const response = await getPageBackground(fileId, pageIndex)

            if (!response.background) {
                throw new Error('No background data received')
            }

            // Create image element
            const img = new Image()
            img.crossOrigin = 'anonymous'

            img.onload = () => {
                console.log(`Background loaded: ${img.width}x${img.height}`)

                // Calculate scale to fit the container
                const scaleX = width / img.width
                const scaleY = height / img.height
                const fitScale = Math.min(scaleX, scaleY)

                setScale(fitScale)
                setBackgroundImage(img)
                setLoading(false)
            }

            img.onerror = (e) => {
                console.error('Failed to load background image:', e)
                setError('Failed to load background image')
                setLoading(false)
            }

            img.src = response.background

        } catch (err) {
            console.error('Error loading background:', err)
            setError(err instanceof Error ? err.message : 'Failed to load background')
            setLoading(false)
        }
    }

    if (loading) {
        return (
            <div
                style={{
                    width,
                    height,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: '#f5f5f5',
                    border: '1px solid #ddd'
                }}
            >
                <div style={{ textAlign: 'center' }}>
                    <div style={{
                        width: '40px',
                        height: '40px',
                        border: '4px solid #f3f3f3',
                        borderTop: '4px solid #3498db',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite',
                        margin: '0 auto 10px'
                    }}></div>
                    <style>{`
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    `}</style>
                    <div>Loading page background...</div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div
                style={{
                    width,
                    height,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: '#fff',
                    border: '1px solid #ddd',
                    color: '#666'
                }}
            >
                <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '18px', marginBottom: '10px' }}>⚠️</div>
                    <div>Error: {error}</div>
                    <button
                        onClick={loadBackgroundImage}
                        style={{
                            marginTop: '10px',
                            padding: '5px 10px',
                            background: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '3px',
                            cursor: 'pointer'
                        }}
                    >
                        Retry
                    </button>
                </div>
            </div>
        )
    }

    if (!backgroundImage) {
        return (
            <div
                style={{
                    width,
                    height,
                    background: '#fff',
                    border: '1px solid #ddd'
                }}
            >
                No background available
            </div>
        )
    }

    // Calculate actual display dimensions
    const displayWidth = backgroundImage.width * scale
    const displayHeight = backgroundImage.height * scale

    return (
        <div style={{ position: 'relative', width, height }}>
            {/* Background Layer - Pixel Perfect PDF Rendering */}
            <Stage
                width={width}
                height={height}
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    zIndex: 1
                }}
            >
                <Layer>
                    <KonvaImage
                        image={backgroundImage}
                        x={(width - displayWidth) / 2}
                        y={(height - displayHeight) / 2}
                        width={displayWidth}
                        height={displayHeight}
                        listening={false} // Background is not interactive
                    />
                </Layer>
            </Stage>

            {/* Overlay Layer - Editable Elements */}
            <div
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    zIndex: 2,
                    pointerEvents: 'auto'
                }}
            >
                {children}
            </div>
        </div>
    )
}

export default BackgroundPDFRenderer
