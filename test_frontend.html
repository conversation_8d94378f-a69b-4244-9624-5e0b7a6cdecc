<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Editor Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        .thumbnail {
            width: 100px;
            height: 140px;
            border: 1px solid #ccc;
            margin: 5px;
            display: inline-block;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>PDF Editor Frontend Test</h1>
    
    <div class="test-section">
        <h2>1. API Connection Test</h2>
        <div id="api-test">
            <div class="spinner"></div>Testing API connection...
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. PDF Upload Test</h2>
        <input type="file" id="pdf-upload" accept=".pdf">
        <button onclick="uploadPDF()">Upload PDF</button>
        <div id="upload-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Thumbnail Loading Test</h2>
        <div id="thumbnail-test"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Content Rendering Test</h2>
        <div id="content-test"></div>
    </div>

    <script>
        let currentFileId = null;
        
        // Test API connection
        async function testAPI() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                document.getElementById('api-test').innerHTML = 
                    `<span style="color: green;">✅ API Connected: ${data.status}</span>`;
                document.getElementById('api-test').className = 'success';
            } catch (error) {
                document.getElementById('api-test').innerHTML = 
                    `<span style="color: red;">❌ API Connection Failed: ${error.message}</span>`;
                document.getElementById('api-test').className = 'error';
            }
        }
        
        // Upload PDF
        async function uploadPDF() {
            const fileInput = document.getElementById('pdf-upload');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a PDF file');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                document.getElementById('upload-result').innerHTML = 
                    '<div class="spinner"></div>Uploading...';
                
                const response = await fetch('/api/upload/', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                currentFileId = data.file_id;
                
                document.getElementById('upload-result').innerHTML = 
                    `<span style="color: green;">✅ Upload successful! File ID: ${data.file_id}, Pages: ${data.pages.length}</span>`;
                
                // Test thumbnails
                testThumbnails(data.file_id, data.pages.length);
                
                // Test content
                testContent(data.pages);
                
            } catch (error) {
                document.getElementById('upload-result').innerHTML = 
                    `<span style="color: red;">❌ Upload failed: ${error.message}</span>`;
            }
        }
        
        // Test thumbnail loading
        async function testThumbnails(fileId, pageCount) {
            const thumbnailDiv = document.getElementById('thumbnail-test');
            thumbnailDiv.innerHTML = '<div class="spinner"></div>Loading thumbnails...';
            
            try {
                const response = await fetch(`/api/thumbnails/${fileId}`);
                const data = await response.json();
                
                let html = `<p style="color: green;">✅ Thumbnails loaded: ${data.thumbnails.length} thumbnails</p>`;
                
                data.thumbnails.forEach((thumb, index) => {
                    const dataUrl = thumb.startsWith('data:') ? thumb : `data:image/png;base64,${thumb}`;
                    html += `<div class="thumbnail" style="background-image: url('${dataUrl}')" title="Page ${index + 1}"></div>`;
                });
                
                thumbnailDiv.innerHTML = html;
                
            } catch (error) {
                thumbnailDiv.innerHTML = 
                    `<span style="color: red;">❌ Thumbnail loading failed: ${error.message}</span>`;
            }
        }
        
        // Test content rendering
        function testContent(pages) {
            const contentDiv = document.getElementById('content-test');
            
            let html = '<h3>Content Analysis:</h3>';
            let totalText = 0, totalImages = 0, totalShapes = 0;
            
            pages.forEach((page, index) => {
                totalText += page.text_blocks.length;
                totalImages += page.images.length;
                totalShapes += page.shapes.length;
                
                html += `<p>Page ${index + 1}: ${page.text_blocks.length} text blocks, ${page.images.length} images, ${page.shapes.length} shapes</p>`;
            });
            
            html += `<p><strong>Total: ${totalText} text blocks, ${totalImages} images, ${totalShapes} shapes</strong></p>`;
            
            if (totalShapes > 0) {
                html += '<p style="color: green;">✅ Shapes detected - vector graphics should render properly</p>';
            } else {
                html += '<p style="color: orange;">⚠️ No shapes detected - complex graphics might not render</p>';
            }
            
            contentDiv.innerHTML = html;
        }
        
        // Run API test on page load
        testAPI();
    </script>
</body>
</html>
