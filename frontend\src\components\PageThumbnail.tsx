import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import './PageThumbnail.css'

interface PageThumbnailProps {
    pageIndex: number
    isActive: boolean
    thumbnail?: string
    onClick: () => void
    isLoading?: boolean
}

const PageThumbnail: React.FC<PageThumbnailProps> = ({
    pageIndex,
    isActive,
    thumbnail,
    onClick,
    isLoading = false
}) => {
    const [imageLoaded, setImageLoaded] = useState(false)
    const [imageError, setImageError] = useState(false)

    useEffect(() => {
        setImageLoaded(false)
        setImageError(false)
    }, [thumbnail])

    const handleImageLoad = () => {
        setImageLoaded(true)
    }

    const handleImageError = () => {
        setImageError(true)
        setImageLoaded(false)
    }

    return (
        <motion.button
            className={`page-thumbnail ${isActive ? 'active' : ''} ${isLoading ? 'loading' : ''}`}
            onClick={onClick}
            title={`Go to page ${pageIndex + 1}`}
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            transition={{ duration: 0.2 }}
        >
            <div className="thumbnail-container">
                {isLoading && (
                    <div className="thumbnail-loading">
                        <div className="loading-spinner"></div>
                        <span className="loading-text">Loading...</span>
                    </div>
                )}
                
                {!isLoading && thumbnail && !imageError && (
                    <>
                        <img
                            src={thumbnail}
                            alt={`Page ${pageIndex + 1} thumbnail`}
                            className={`thumbnail-image ${imageLoaded ? 'loaded' : ''}`}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                        />
                        {!imageLoaded && (
                            <div className="thumbnail-loading">
                                <div className="loading-spinner"></div>
                            </div>
                        )}
                    </>
                )}
                
                {(!thumbnail || imageError) && !isLoading && (
                    <div className="thumbnail-fallback">
                        <div className="fallback-icon">📄</div>
                        <span className="fallback-text">Page {pageIndex + 1}</span>
                    </div>
                )}
            </div>
            
            <div className="thumbnail-label">
                <span className="page-number">{pageIndex + 1}</span>
            </div>
            
            {isActive && (
                <motion.div
                    className="active-indicator"
                    layoutId="activeIndicator"
                    initial={false}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
            )}
        </motion.button>
    )
}

export default PageThumbnail
