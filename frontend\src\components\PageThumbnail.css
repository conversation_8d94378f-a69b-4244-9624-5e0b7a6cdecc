/* Page Thumbnail Component */
.page-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--surface-tertiary);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  padding: var(--spacing-xs);
  gap: var(--spacing-xs);
}

.page-thumbnail:hover {
  border-color: var(--info);
  background: var(--surface-secondary);
  box-shadow: var(--shadow-sm);
}

.page-thumbnail.active {
  border-color: var(--info);
  background: var(--info);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.page-thumbnail.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Thumbnail Container */
.thumbnail-container {
  position: relative;
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  overflow: hidden;
  background: var(--surface-primary);
}

.page-thumbnail.active .thumbnail-container {
  background: rgba(255, 255, 255, 0.1);
}

/* Thumbnail Image */
.thumbnail-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-xs);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.thumbnail-image.loaded {
  opacity: 1;
}

/* Loading State */
.thumbnail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  color: var(--text-muted);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--info);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Fallback State */
.thumbnail-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  color: var(--text-muted);
  text-align: center;
}

.page-thumbnail.active .thumbnail-fallback {
  color: var(--text-inverse);
}

.fallback-icon {
  font-size: 2rem;
  opacity: 0.6;
}

.fallback-text {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Thumbnail Label */
.thumbnail-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.page-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.page-thumbnail.active .page-number {
  color: var(--text-inverse);
}

/* Active Indicator */
.active-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(var(--info-rgb), 0.1) 0%,
    rgba(var(--info-rgb), 0.05) 100%
  );
  border-radius: var(--radius-md);
  pointer-events: none;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus States */
.page-thumbnail:focus-visible {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .thumbnail-container {
    height: 100px;
  }
  
  .page-number {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .thumbnail-container {
    height: 80px;
  }
  
  .page-number {
    font-size: 0.75rem;
  }
  
  .fallback-icon {
    font-size: 1.5rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .page-thumbnail {
    border-width: 3px;
  }
  
  .page-thumbnail.active {
    border-color: var(--text-inverse);
  }
  
  .loading-spinner {
    border-width: 3px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .page-thumbnail {
    transition: none;
  }
  
  .thumbnail-image {
    transition: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .page-thumbnail {
    display: none !important;
  }
}
