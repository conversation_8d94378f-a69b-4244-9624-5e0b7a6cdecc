/* PDF Editor Content */
.pdf-editor-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.no-page-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--text-muted);
  font-size: 1.125rem;
  background: var(--surface-tertiary);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-light);
}

/* Page Thumbnails Sidebar */
.page-thumbnails {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.thumbnails-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
}

.thumbnails-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 400px;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
}

.thumbnail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--surface-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--accent-bg);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.thumbnail:hover {
  border-color: var(--info);
  transform: translateY(-1px);
}

.thumbnail:hover::before {
  opacity: 0.1;
}

.thumbnail.active {
  border-color: var(--info);
  background: var(--info);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.thumbnail.active::before {
  opacity: 0;
}

.thumbnail-number {
  position: relative;
  z-index: 1;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .page-thumbnails {
    display: none;
  }
}

@media (max-width: 768px) {
  .pdf-editor-content {
    padding: var(--spacing-sm);
  }
  
  .no-page-data {
    height: 200px;
    font-size: 1rem;
    margin: var(--spacing-md);
  }
}

/* Focus states */
.thumbnail:focus-visible {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

/* Loading state */
.pdf-editor-content.loading {
  opacity: 0.7;
  pointer-events: none;
}

.pdf-editor-content.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  border: 4px solid var(--surface-tertiary);
  border-top: 4px solid var(--info);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1000;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .thumbnail {
    border-width: 3px;
  }
  
  .thumbnail.active {
    border-color: var(--text-inverse);
  }
}

/* Print styles */
@media print {
  .page-thumbnails {
    display: none !important;
  }
}
