#!/usr/bin/env python3

import requests
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def setup_headless_browser():
    """Setup headless Chrome browser"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-extensions")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"❌ Failed to setup Chrome driver: {e}")
        print("Please install ChromeDriver: https://chromedriver.chromium.org/")
        return None

def upload_pdf_via_browser(driver, pdf_path):
    """Upload PDF through the browser interface"""
    try:
        print("🌐 Navigating to application...")
        driver.get("http://localhost:5173")
        
        # Wait for page to load
        time.sleep(3)
        
        # Click "Edit PDF" button
        print("📝 Clicking Edit PDF...")
        edit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Edit PDF')]"))
        )
        edit_button.click()
        
        # Wait for upload interface
        time.sleep(2)
        
        # Find file input and upload
        print("📤 Uploading PDF...")
        file_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
        )
        
        # Convert relative path to absolute
        abs_pdf_path = os.path.abspath(pdf_path)
        file_input.send_keys(abs_pdf_path)
        
        # Wait for upload to complete and editor to load
        print("⏳ Waiting for editor to load...")
        time.sleep(10)  # Give time for PDF processing and rendering
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

def capture_frontend_pages(driver, num_pages):
    """Capture screenshots of each page from the frontend"""
    screenshots = []
    
    try:
        for page_num in range(num_pages):
            print(f"📸 Capturing page {page_num + 1}...")
            
            # Navigate to specific page if needed
            if page_num > 0:
                # Look for page navigation buttons
                try:
                    next_button = driver.find_element(By.XPATH, f"//button[contains(text(), '{page_num + 1}')]")
                    next_button.click()
                    time.sleep(2)
                except:
                    print(f"⚠️  Could not navigate to page {page_num + 1}")
            
            # Take screenshot of the main content area
            try:
                # Find the PDF editor canvas/content area
                canvas_element = driver.find_element(By.CSS_SELECTOR, ".pdf-page-editor, .stage-container, canvas")
                
                # Take screenshot of just the canvas area
                screenshot_path = f"frontend_page_{page_num + 1}.png"
                canvas_element.screenshot(screenshot_path)
                screenshots.append(screenshot_path)
                
                print(f"✅ Saved {screenshot_path}")
                
            except Exception as e:
                # Fallback: take full page screenshot
                screenshot_path = f"frontend_page_{page_num + 1}_full.png"
                driver.save_screenshot(screenshot_path)
                screenshots.append(screenshot_path)
                print(f"⚠️  Saved full page screenshot: {screenshot_path}")
        
        return screenshots
        
    except Exception as e:
        print(f"❌ Screenshot capture failed: {e}")
        return []

def compare_images(original_path, frontend_path, output_path):
    """Compare original vs frontend images and create diff"""
    try:
        # Load images
        original = Image.open(original_path).convert('RGB')
        frontend = Image.open(frontend_path).convert('RGB')
        
        # Resize to same dimensions for comparison
        width = min(original.width, frontend.width)
        height = min(original.height, frontend.height)
        
        original = original.resize((width, height))
        frontend = frontend.resize((width, height))
        
        # Convert to numpy arrays
        orig_array = np.array(original)
        front_array = np.array(frontend)
        
        # Calculate difference
        diff_array = np.abs(orig_array.astype(int) - front_array.astype(int))
        
        # Create difference image
        diff_image = Image.fromarray(diff_array.astype(np.uint8))
        
        # Create comparison image (side by side)
        comparison = Image.new('RGB', (width * 3, height))
        comparison.paste(original, (0, 0))
        comparison.paste(frontend, (width, 0))
        comparison.paste(diff_image, (width * 2, 0))
        
        # Add labels
        draw = ImageDraw.Draw(comparison)
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((10, 10), "ORIGINAL", fill="red", font=font)
        draw.text((width + 10, 10), "FRONTEND", fill="blue", font=font)
        draw.text((width * 2 + 10, 10), "DIFFERENCE", fill="green", font=font)
        
        comparison.save(output_path)
        
        # Calculate similarity percentage
        total_pixels = width * height * 3
        diff_pixels = np.sum(diff_array > 30)  # Threshold for significant difference
        similarity = (1 - diff_pixels / total_pixels) * 100
        
        return similarity
        
    except Exception as e:
        print(f"❌ Image comparison failed: {e}")
        return 0

def main():
    """Main comparison function"""
    print("🔍 PDF RENDERING COMPARISON SYSTEM")
    print("=" * 50)
    
    pdf_path = "backend/rapport_PL - RADIOFRANCE - Scénario diurne_2025_04.pdf"
    
    # Check if reference images exist
    reference_files = ["reference_page_1.png", "reference_page_2.png"]
    for ref_file in reference_files:
        if not os.path.exists(ref_file):
            print(f"❌ Reference file missing: {ref_file}")
            print("Please run the diagnostic script first to generate reference images")
            return
    
    # Setup browser
    driver = setup_headless_browser()
    if not driver:
        return
    
    try:
        # Upload PDF and capture frontend screenshots
        if upload_pdf_via_browser(driver, pdf_path):
            frontend_screenshots = capture_frontend_pages(driver, 2)
            
            if frontend_screenshots:
                print(f"\n📊 COMPARISON RESULTS:")
                print("=" * 50)
                
                # Compare each page
                for i, frontend_screenshot in enumerate(frontend_screenshots):
                    reference_file = f"reference_page_{i + 1}.png"
                    comparison_file = f"comparison_page_{i + 1}.png"
                    
                    if os.path.exists(reference_file):
                        similarity = compare_images(reference_file, frontend_screenshot, comparison_file)
                        
                        print(f"📄 Page {i + 1}:")
                        print(f"  📸 Reference: {reference_file}")
                        print(f"  🌐 Frontend: {frontend_screenshot}")
                        print(f"  📊 Similarity: {similarity:.1f}%")
                        print(f"  🔍 Comparison: {comparison_file}")
                        
                        if similarity < 70:
                            print(f"  ❌ MAJOR DIFFERENCES DETECTED!")
                        elif similarity < 90:
                            print(f"  ⚠️  Minor differences detected")
                        else:
                            print(f"  ✅ Good similarity")
                        print()
                
                print("🎯 ANALYSIS:")
                print("- Check comparison_page_X.png files to see exact differences")
                print("- Left: Original PDF, Middle: Frontend render, Right: Differences")
                print("- Red areas in difference image show missing/incorrect content")
                
            else:
                print("❌ Failed to capture frontend screenshots")
        else:
            print("❌ Failed to upload PDF")
            
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
