#!/usr/bin/env python3

import requests
import json
import fitz  # PyMuPDF
import base64
from PIL import Image
import io
import os
from selenium import webdriver
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def setup_headless_browser():
    """Setup headless Chrome browser for screenshots"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"❌ Failed to setup Chrome driver: {e}")
        print("Please install ChromeDriver or use a different browser")
        return None

def capture_original_pdf_pages(pdf_path):
    """Capture screenshots of original PDF pages"""
    print("📸 Capturing original PDF pages...")
    
    doc = fitz.open(pdf_path)
    original_images = []
    
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        
        # Render at high resolution
        mat = fitz.Matrix(2.0, 2.0)  # 2x zoom
        pix = page.get_pixmap(matrix=mat)
        img_data = pix.tobytes("png")
        
        # Save original page image
        img_path = f"original_page_{page_num + 1}.png"
        with open(img_path, "wb") as f:
            f.write(img_data)
        
        original_images.append(img_path)
        print(f"  ✅ Captured original page {page_num + 1}")
    
    doc.close()
    return original_images

def upload_pdf_and_get_data(pdf_path):
    """Upload PDF to our system and get the parsed data"""
    print("📤 Uploading PDF to system...")
    
    try:
        with open(pdf_path, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/upload/', files=files)
        
        if response.status_code != 200:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
        
        data = response.json()
        print(f"✅ Upload successful - File ID: {data['file_id']}, Pages: {len(data['pages'])}")
        return data
        
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

def analyze_parsed_content(pages_data):
    """Analyze what content was extracted from the PDF"""
    print("\n🔍 Analyzing parsed content...")
    
    total_text = 0
    total_images = 0
    total_shapes = 0
    
    for i, page in enumerate(pages_data):
        text_blocks = len(page.get("text_blocks", []))
        images = len(page.get("images", []))
        shapes = len(page.get("shapes", []))
        
        total_text += text_blocks
        total_images += images
        total_shapes += shapes
        
        print(f"  Page {i+1}: {text_blocks} text blocks, {images} images, {shapes} shapes")
        
        # Analyze shapes in detail
        for shape in page.get("shapes", []):
            items = shape.get("items", [])
            print(f"    Shape {shape.get('id', 'unknown')}: {len(items)} items")
    
    print(f"\n📊 Total extracted: {total_text} text, {total_images} images, {total_shapes} shapes")
    
    # Check for potential issues
    if total_shapes == 0:
        print("⚠️  WARNING: No shapes detected - charts/tables might be missing")
    if total_images == 0:
        print("⚠️  WARNING: No images detected - graphics might be missing")
    
    return {
        "total_text": total_text,
        "total_images": total_images,
        "total_shapes": total_shapes
    }

def capture_rendered_pages_with_browser(file_id, page_count):
    """Capture screenshots of how pages render in our editor"""
    print("📸 Capturing rendered pages from browser...")
    
    driver = setup_headless_browser()
    if not driver:
        return []
    
    rendered_images = []
    
    try:
        # Navigate to the editor
        driver.get("http://localhost:5173")
        
        # Wait for page to load
        time.sleep(3)
        
        # Click "Edit PDF" button
        edit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Edit PDF')]"))
        )
        edit_button.click()
        
        # Upload the file (this is tricky with headless browser, so we'll use the API data)
        # For now, let's just check if we can access the editor interface
        
        time.sleep(5)  # Wait for interface to load
        
        # Take screenshot of current state
        screenshot_path = "rendered_interface.png"
        driver.save_screenshot(screenshot_path)
        rendered_images.append(screenshot_path)
        print(f"  ✅ Captured interface screenshot")
        
    except Exception as e:
        print(f"❌ Browser capture error: {e}")
    
    finally:
        driver.quit()
    
    return rendered_images

def test_thumbnail_loading(file_id, page_count):
    """Test if thumbnails load properly"""
    print("🖼️  Testing thumbnail loading...")
    
    try:
        response = requests.get(f'http://localhost:8000/thumbnails/{file_id}')
        
        if response.status_code != 200:
            print(f"❌ Thumbnail request failed: {response.status_code}")
            return False
        
        data = response.json()
        thumbnails = data.get('thumbnails', [])
        
        if len(thumbnails) != page_count:
            print(f"❌ Expected {page_count} thumbnails, got {len(thumbnails)}")
            return False
        
        # Check thumbnail quality
        for i, thumb in enumerate(thumbnails):
            if not thumb or len(thumb) < 100:
                print(f"❌ Thumbnail {i+1} is empty or too small")
                return False
            
            # Save thumbnail for inspection
            try:
                # Remove data URL prefix if present
                if thumb.startswith('data:image'):
                    thumb = thumb.split(',')[1]
                
                img_data = base64.b64decode(thumb)
                thumb_path = f"thumbnail_{i+1}.png"
                with open(thumb_path, "wb") as f:
                    f.write(img_data)
                print(f"  ✅ Thumbnail {i+1} saved ({len(img_data)} bytes)")
                
            except Exception as e:
                print(f"❌ Failed to decode thumbnail {i+1}: {e}")
                return False
        
        print("✅ All thumbnails loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Thumbnail test error: {e}")
        return False

def main():
    """Main testing function"""
    pdf_path = "backend/rapport_PL - RADIOFRANCE - Scénario diurne_2025_04.pdf"
    
    print("🧪 SYSTEMATIC PDF EDITOR TESTING")
    print("=" * 50)
    
    # Step 1: Capture original PDF
    original_images = capture_original_pdf_pages(pdf_path)
    
    # Step 2: Upload and parse PDF
    upload_data = upload_pdf_and_get_data(pdf_path)
    if not upload_data:
        return
    
    file_id = upload_data['file_id']
    pages_data = upload_data['pages']
    page_count = len(pages_data)
    
    # Step 3: Analyze parsed content
    content_analysis = analyze_parsed_content(pages_data)
    
    # Step 4: Test thumbnail loading
    thumbnails_ok = test_thumbnail_loading(file_id, page_count)
    
    # Step 5: Capture rendered pages
    rendered_images = capture_rendered_pages_with_browser(file_id, page_count)
    
    # Step 6: Summary
    print("\n" + "=" * 50)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    print(f"📄 Original PDF: {page_count} pages")
    print(f"📊 Content extracted: {content_analysis['total_text']} text, {content_analysis['total_images']} images, {content_analysis['total_shapes']} shapes")
    print(f"🖼️  Thumbnails: {'✅ Working' if thumbnails_ok else '❌ Failed'}")
    print(f"📸 Screenshots: {len(original_images)} original, {len(rendered_images)} rendered")
    
    # Identify issues
    issues = []
    if not thumbnails_ok:
        issues.append("Thumbnail loading still broken")
    if content_analysis['total_shapes'] == 0:
        issues.append("No vector graphics/shapes detected")
    if content_analysis['total_images'] == 0:
        issues.append("No images detected")
    
    if issues:
        print("\n❌ ISSUES FOUND:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("\n✅ No major issues detected")
    
    print(f"\n📁 Files generated:")
    for img in original_images + rendered_images:
        if os.path.exists(img):
            print(f"  - {img}")

if __name__ == "__main__":
    main()
