import React, { useState } from 'react'
import { ThemeProvider } from './contexts/ThemeContext'
import Header from './components/ui/Header'
import WelcomeScreen from './components/ui/WelcomeScreen'
import ActionSelection from './components/ActionSelection'
import FeatureSelection from './components/ui/FeatureSelection'
import Footer from './components/ui/Footer'
import LoadingOverlay from './components/ui/LoadingOverlay'
import PDFEditor from './components/PDFEditor'
import PDFSplitter from './components/PDFSplitter'
import { uploadPDF, savePDF } from './services/api'
import { SceneData } from './types'
import './i18n'
import './styles/glassmorphism.css'
import './App.css'

type AppState = 'action-selection' | 'upload' | 'editing' | 'splitting'
type SelectedAction = 'edit' | 'split' | null

function App() {
    const [appState, setAppState] = useState<AppState>('action-selection')
    const [selectedAction, setSelectedAction] = useState<SelectedAction>(null)
    const [sceneData, setSceneData] = useState<SceneData | null>(null)
    const [fileId, setFileId] = useState<number | null>(null)
    const [filename, setFilename] = useState<string>('')
    const [totalPages, setTotalPages] = useState<number>(0)
    const [isLoading, setIsLoading] = useState(false)

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (!file) return

        setIsLoading(true)
        try {
            const response = await uploadPDF(file)

            // Initialize all elements with 'original' action to track changes
            const initializedPages = response.pages.map(page => ({
                ...page,
                text_blocks: page.text_blocks.map(block => ({
                    ...block,
                    action: 'original' as const
                })),
                images: page.images.map(img => ({
                    ...img,
                    action: 'original' as const
                })),
                shapes: page.shapes.map(shape => ({
                    ...shape,
                    action: 'original' as const
                }))
            }))

            setFileId(response.file_id)
            setFilename(response.filename)
            setTotalPages(response.pages.length)

            // Go directly to the chosen action
            if (selectedAction === 'edit') {
                setSceneData({ pages: initializedPages })
                setAppState('editing')
            } else if (selectedAction === 'split') {
                setAppState('splitting')
            }
        } catch (error) {
            console.error('Error uploading PDF:', error)
            alert('Error uploading PDF. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const handleSave = async () => {
        if (!sceneData || fileId === null) return

        setIsLoading(true)
        try {
            console.log('Saving PDF with data:', sceneData)
            console.log('File ID:', fileId)

            const blob = await savePDF(fileId, sceneData)

            // Create download link
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `edited_${filename}`
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
        } catch (error) {
            console.error('Error saving PDF:', error)
            console.error('Error details:', error.response?.data)
            alert('Error saving PDF. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const handleSceneUpdate = (updatedScene: SceneData) => {
        setSceneData(updatedScene)
    }

    const handleActionSelect = (action: 'edit' | 'split') => {
        setSelectedAction(action)
        setAppState('upload')
    }

    const handleBackToActionSelection = () => {
        setAppState('action-selection')
        setSelectedAction(null)
        setSceneData(null)
        setFileId(null)
        setFilename('')
        setTotalPages(0)
    }

    const handleBackToUpload = () => {
        setAppState('upload')
        setSceneData(null)
        setFileId(null)
        setFilename('')
        setTotalPages(0)
    }

    return (
        <ThemeProvider>
            <div className="app">
                <Header
                    onSave={handleSave}
                    canSave={appState === 'editing' && !!sceneData}
                    isLoading={isLoading}
                />

                <main className="app-main">
                    {appState === 'action-selection' && !isLoading && (
                        <ActionSelection onActionSelect={handleActionSelect} />
                    )}

                    {appState === 'upload' && !isLoading && (
                        <WelcomeScreen
                            onFileUpload={handleFileUpload}
                            isLoading={isLoading}
                            selectedAction={selectedAction}
                            onBack={handleBackToActionSelection}
                        />
                    )}

                    {appState === 'editing' && sceneData && !isLoading && (
                        <PDFEditor
                            sceneData={sceneData}
                            onSceneUpdate={handleSceneUpdate}
                        />
                    )}

                    {appState === 'splitting' && fileId !== null && !isLoading && (
                        <PDFSplitter
                            fileId={fileId}
                            filename={filename}
                            totalPages={totalPages}
                            onBack={handleBackToUpload}
                        />
                    )}
                </main>

                {(appState === 'action-selection' || appState === 'upload') && <Footer />}

                <LoadingOverlay isVisible={isLoading} />
            </div>
        </ThemeProvider>
    )
}

export default App
