import React from 'react'
import { Group, Line, Rect, Path } from 'react-konva'
import { ShapeBlock } from '../types'

interface ShapeRendererProps {
    shape: ShapeBlock
    onClick?: () => void
}

const ShapeRenderer: React.FC<ShapeRendererProps> = ({ shape, onClick }) => {
    const renderShapeItems = () => {
        return shape.items.map((item: any, index: number) => {
            try {
                if (typeof item === 'object' && item.type) {
                    switch (item.type) {
                        case 'line':
                            if (item.points && item.points.length >= 4) {
                                return (
                                    <Line
                                        key={`${shape.id}-line-${index}`}
                                        points={item.points.flat()}
                                        stroke={shape.stroke ? `#${shape.stroke.toString(16).padStart(6, '0')}` : '#000000'}
                                        strokeWidth={shape.width || 1}
                                    />
                                )
                            }
                            break
                        
                        case 'rect':
                            if (item.rect && item.rect.length >= 4) {
                                const [x, y, width, height] = item.rect
                                return (
                                    <Rect
                                        key={`${shape.id}-rect-${index}`}
                                        x={x}
                                        y={y}
                                        width={width}
                                        height={height}
                                        fill={shape.fill ? `#${shape.fill.toString(16).padStart(6, '0')}` : 'transparent'}
                                        stroke={shape.stroke ? `#${shape.stroke.toString(16).padStart(6, '0')}` : '#000000'}
                                        strokeWidth={shape.width || 1}
                                    />
                                )
                            }
                            break
                        
                        case 'curve':
                            if (item.points && item.points.length >= 4) {
                                // For curves, we'll approximate with a line for now
                                return (
                                    <Line
                                        key={`${shape.id}-curve-${index}`}
                                        points={item.points.flat()}
                                        stroke={shape.stroke ? `#${shape.stroke.toString(16).padStart(6, '0')}` : '#000000'}
                                        strokeWidth={shape.width || 1}
                                        tension={0.5} // Add some curve smoothing
                                    />
                                )
                            }
                            break
                        
                        default:
                            // For other types, try to render as a basic shape if we have rect info
                            if (shape.rect && shape.rect.length >= 4) {
                                const [x, y, width, height] = shape.rect
                                return (
                                    <Rect
                                        key={`${shape.id}-fallback-${index}`}
                                        x={x}
                                        y={y}
                                        width={width}
                                        height={height}
                                        fill={shape.fill ? `#${shape.fill.toString(16).padStart(6, '0')}` : 'transparent'}
                                        stroke={shape.stroke ? `#${shape.stroke.toString(16).padStart(6, '0')}` : '#000000'}
                                        strokeWidth={shape.width || 1}
                                    />
                                )
                            }
                            break
                    }
                } else {
                    // Handle legacy format - try to interpret raw drawing commands
                    if (Array.isArray(item) && item.length > 0) {
                        const command = item[0]
                        if (command === 'l' && item.length >= 5) {
                            // Line command: ['l', x1, y1, x2, y2]
                            return (
                                <Line
                                    key={`${shape.id}-legacy-line-${index}`}
                                    points={[item[1], item[2], item[3], item[4]]}
                                    stroke={shape.stroke ? `#${shape.stroke.toString(16).padStart(6, '0')}` : '#000000'}
                                    strokeWidth={shape.width || 1}
                                />
                            )
                        } else if (command === 're' && item.length >= 5) {
                            // Rectangle command: ['re', x, y, width, height]
                            return (
                                <Rect
                                    key={`${shape.id}-legacy-rect-${index}`}
                                    x={item[1]}
                                    y={item[2]}
                                    width={item[3]}
                                    height={item[4]}
                                    fill={shape.fill ? `#${shape.fill.toString(16).padStart(6, '0')}` : 'transparent'}
                                    stroke={shape.stroke ? `#${shape.stroke.toString(16).padStart(6, '0')}` : '#000000'}
                                    strokeWidth={shape.width || 1}
                                />
                            )
                        }
                    }
                }
            } catch (error) {
                console.warn(`Error rendering shape item ${index}:`, error)
            }
            return null
        }).filter(Boolean)
    }

    return (
        <Group
            onClick={onClick}
            onTap={onClick}
        >
            {renderShapeItems()}
        </Group>
    )
}

export default ShapeRenderer
