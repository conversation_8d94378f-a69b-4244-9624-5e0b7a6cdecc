#!/usr/bin/env python3

import requests
import json
import fitz  # PyMuPDF
import base64
import os


def analyze_original_pdf(pdf_path):
    """Analyze the original PDF to understand its structure"""
    print("🔍 Analyzing original PDF structure...")

    doc = fitz.open(pdf_path)

    print(f"📄 PDF Info:")
    print(f"  - Pages: {len(doc)}")
    print(f"  - Title: {doc.metadata.get('title', 'N/A')}")
    print(f"  - Author: {doc.metadata.get('author', 'N/A')}")

    total_elements = 0

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)

        # Get text
        text_dict = page.get_text("dict")
        text_blocks = len([b for b in text_dict["blocks"] if "lines" in b])

        # Get images
        images = len(page.get_images())

        # Get drawings (vector graphics)
        drawings = len(page.get_drawings())

        # Get annotations
        annotations = len(list(page.annots()))

        total_elements += text_blocks + images + drawings + annotations

        print(
            f"  Page {page_num + 1}: {text_blocks} text blocks, {images} images, {drawings} drawings, {annotations} annotations"
        )

        # Analyze drawings in detail
        if drawings > 0:
            page_drawings = page.get_drawings()
            for i, drawing in enumerate(page_drawings[:3]):  # Show first 3
                items = len(drawing.get("items", []))
                rect = drawing.get("rect", [0, 0, 0, 0])
                print(f"    Drawing {i+1}: {items} items, rect: {rect}")

    doc.close()

    print(f"\n📊 Total elements in original: {total_elements}")
    return total_elements


def test_upload_and_parsing(pdf_path):
    """Upload PDF and test parsing"""
    print("\n📤 Testing upload and parsing...")

    try:
        with open(pdf_path, "rb") as f:
            files = {"file": f}
            response = requests.post("http://localhost:8000/upload/", files=files)

        if response.status_code != 200:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None

        data = response.json()
        file_id = data["file_id"]
        pages = data["pages"]

        print(f"✅ Upload successful - File ID: {file_id}")

        # Analyze parsed content
        total_parsed = 0
        for i, page in enumerate(pages):
            text_blocks = len(page.get("text_blocks", []))
            images = len(page.get("images", []))
            shapes = len(page.get("shapes", []))

            total_parsed += text_blocks + images + shapes

            print(f"  Page {i+1}: {text_blocks} text, {images} images, {shapes} shapes")

            # Show shape details
            for shape in page.get("shapes", [])[:2]:  # Show first 2 shapes
                items = len(shape.get("items", []))
                rect = shape.get("rect", [0, 0, 0, 0])
                print(f"    Shape {shape.get('id')}: {items} items, rect: {rect}")

        print(f"\n📊 Total elements parsed: {total_parsed}")

        return data

    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None


def test_thumbnails(file_id, expected_pages):
    """Test thumbnail generation"""
    print(f"\n🖼️  Testing thumbnails for file {file_id}...")

    try:
        # Test bulk thumbnail endpoint
        response = requests.get(f"http://localhost:8000/thumbnails/{file_id}")

        if response.status_code != 200:
            print(f"❌ Bulk thumbnails failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False

        data = response.json()
        thumbnails = data.get("thumbnails", [])

        print(
            f"📊 Bulk endpoint returned {len(thumbnails)} thumbnails (expected {expected_pages})"
        )

        if len(thumbnails) != expected_pages:
            print(f"❌ Thumbnail count mismatch!")
            return False

        # Test individual thumbnails
        for i in range(expected_pages):
            try:
                response = requests.get(
                    f"http://localhost:8000/thumbnail/{file_id}/{i}"
                )
                if response.status_code == 200:
                    thumb_data = response.json()
                    thumb_size = len(thumb_data.get("thumbnail", ""))
                    print(f"  ✅ Thumbnail {i+1}: {thumb_size} characters")
                else:
                    print(f"  ❌ Thumbnail {i+1} failed: {response.status_code}")
                    return False
            except Exception as e:
                print(f"  ❌ Thumbnail {i+1} error: {e}")
                return False

        # Save first thumbnail for inspection
        if thumbnails and len(thumbnails) > 0:
            try:
                thumb = thumbnails[0]
                if thumb.startswith("data:image"):
                    thumb = thumb.split(",")[1]

                img_data = base64.b64decode(thumb)
                with open("test_thumbnail.png", "wb") as f:
                    f.write(img_data)
                print(
                    f"💾 Saved first thumbnail as test_thumbnail.png ({len(img_data)} bytes)"
                )
            except Exception as e:
                print(f"⚠️  Could not save thumbnail: {e}")

        print("✅ All thumbnails working")
        return True

    except Exception as e:
        print(f"❌ Thumbnail test error: {e}")
        return False


def create_reference_images(pdf_path):
    """Create reference images from original PDF"""
    print(f"\n📸 Creating reference images from original PDF...")

    doc = fitz.open(pdf_path)

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)

        # High resolution render
        mat = fitz.Matrix(2.0, 2.0)
        pix = page.get_pixmap(matrix=mat)

        img_path = f"reference_page_{page_num + 1}.png"
        pix.save(img_path)

        print(f"  ✅ Saved {img_path} ({pix.width}x{pix.height})")

    doc.close()


def main():
    """Main diagnostic function"""
    pdf_path = "backend/rapport_PL - RADIOFRANCE - Scénario diurne_2025_04.pdf"

    print("🧪 SYSTEMATIC PDF DIAGNOSTIC")
    print("=" * 50)

    # Check if PDF exists
    if not os.path.exists(pdf_path):
        print(f"❌ PDF not found: {pdf_path}")
        return

    # Step 1: Analyze original PDF
    original_elements = analyze_original_pdf(pdf_path)

    # Step 2: Test upload and parsing
    upload_data = test_upload_and_parsing(pdf_path)
    if not upload_data:
        print("❌ Cannot proceed - upload failed")
        return

    file_id = upload_data["file_id"]
    pages = upload_data["pages"]

    # Step 3: Test thumbnails
    thumbnails_ok = test_thumbnails(file_id, len(pages))

    # Step 4: Create reference images
    create_reference_images(pdf_path)

    # Step 5: Calculate content preservation
    total_parsed = sum(
        len(page.get("text_blocks", []))
        + len(page.get("images", []))
        + len(page.get("shapes", []))
        for page in pages
    )

    preservation_ratio = (
        (total_parsed / original_elements * 100) if original_elements > 0 else 0
    )

    # Final report
    print("\n" + "=" * 50)
    print("📋 DIAGNOSTIC RESULTS")
    print("=" * 50)

    print(f"📄 Original elements: {original_elements}")
    print(f"📊 Parsed elements: {total_parsed}")
    print(f"📈 Preservation ratio: {preservation_ratio:.1f}%")
    print(f"🖼️  Thumbnails: {'✅ Working' if thumbnails_ok else '❌ Broken'}")

    # Issue identification
    issues = []
    if not thumbnails_ok:
        issues.append("❌ ISSUE 1: Thumbnail loading is broken")

    if preservation_ratio < 50:
        issues.append("❌ ISSUE 2: Major content loss during parsing")
    elif preservation_ratio < 80:
        issues.append("⚠️  ISSUE 2: Moderate content loss during parsing")

    if issues:
        print(f"\n🚨 ISSUES IDENTIFIED:")
        for issue in issues:
            print(f"  {issue}")

        print(f"\n🔧 NEXT STEPS:")
        if not thumbnails_ok:
            print("  1. Debug thumbnail generation pipeline")
        if preservation_ratio < 80:
            print("  2. Improve PDF parsing to capture more elements")
            print("  3. Add better vector graphics handling")
    else:
        print(f"\n✅ No major issues found!")

    print(f"\n📁 Generated files:")
    for i in range(len(pages)):
        ref_file = f"reference_page_{i+1}.png"
        if os.path.exists(ref_file):
            print(f"  - {ref_file}")
    if os.path.exists("test_thumbnail.png"):
        print(f"  - test_thumbnail.png")


if __name__ == "__main__":
    main()
